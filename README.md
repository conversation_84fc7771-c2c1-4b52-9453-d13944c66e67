# BackStagePro

## 项目介绍
BackStagePro 是一个基于 .NET 8 构建的现代化后台管理系统，专注于提供高效、稳定、安全的后端服务解决方案。本项目采用领域驱动设计（DDD）架构，支持多种部署方式，为开发者提供灵活易用的后台管理功能。

## 技术栈
- 框架：.NET 8
- ORM：Entity Framework Core
- 数据库：SQL Server
- 认证：JWT Bearer Token
- API文档：Swagger/OpenAPI
- 缓存：Redis (可选)
- 日志：Serilog
- 单元测试：xUnit

## 项目架构
项目采用领域驱动设计（DDD）架构，分为以下层次：

```
admin-backend/
├── src/
│   ├── Api/                 # API层：处理HTTP请求，路由和控制器
│   ├── Application/         # 应用层：处理业务用例和协调领域对象
│   │   ├── Common/         # 通用组件
│   │   ├── Dto/           # 数据传输对象
│   │   └── Services/      # 应用服务
│   ├── Domain/             # 领域层：核心业务逻辑和规则
│   │   ├── Entities/      # 领域实体
│   │   ├── Enum/         # 枚举定义
│   │   └── Repositories/  # 仓储接口
│   └── Infrastructure/     # 基础设施层：技术实现细节
│       ├── Data/          # 数据访问
│       └── Repositories/  # 仓储实现
└── tests/                  # 测试项目
```

## 核心功能
- [x] 用户认证与授权
- [x] 角色权限管理
- [x] 部门管理
- [x] 用户管理
- [x] 菜单管理
- [x] 操作日志
- [ ] 系统监控
- [ ] 定时任务

## 快速开始

### 环境要求
- .NET 8 SDK
- SQL Server 2019+
- Visual Studio 2022 或 VS Code
- Git

### 安装步骤
1. 克隆仓库
```bash
git clone https://github.com/your-username/back-stage-pro.git
cd back-stage-pro
```

2. 还原依赖
```bash
dotnet restore
```

3. 配置数据库
- 打开 `appsettings.json`
- 修改数据库连接字符串

4. 运行数据库迁移
```bash
dotnet ef database update
```

5. 运行项目
```bash
dotnet run --project src/Api/Api.csproj
```

## 开发规范
1. 代码风格
   - 使用 C# 代码规范
   - 使用 async/await 进行异步编程
   - 遵循 SOLID 原则

2. Git提交规范
   - feat: 新功能
   - fix: 修复bug
   - docs: 文档更新
   - style: 代码格式调整
   - refactor: 重构
   - test: 测试相关
   - chore: 构建过程或辅助工具的变动

## 部署说明
支持多种部署方式：
- IIS部署
- Docker容器化部署
- Azure云服务部署

## 贡献指南
1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 版本历史
- v1.0.0 (2024-01) - 初始版本发布
  - 基础框架搭建
  - 用户认证与授权
  - 基础CRUD功能

## 许可证
本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式
- 项目维护者：[Your Name]
- 邮箱：[<EMAIL>]
- 项目地址：[project-url]

## 致谢
感谢所有为这个项目做出贡献的开发者！
