# API 接口文档

## 1. 概述
本文档描述了后台管理系统的API接口规范和使用说明。所有接口均遵循RESTful设计原则，使用JWT进行身份认证。

## 2. 接口规范

### 2.1 请求格式
- 基础URL: `https://api.example.com/v1`
- Content-Type: `application/json`
- Authorization: `Bearer {token}`

### 2.2 响应格式
```json
{
    "code": 200,           // 状态码
    "message": "success",  // 响应消息
    "data": {             // 响应数据
        // 具体数据结构
    }
}
```

### 2.3 状态码说明
- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器错误

## 3. 认证接口

### 3.1 用户登录
```http
POST /auth/login
Content-Type: application/json

{
    "username": "string",
    "password": "string"
}
```

响应：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "token": "string",
        "refreshToken": "string",
        "expiresIn": 3600
    }
}
```

### 3.2 刷新Token
```http
POST /auth/refresh
Content-Type: application/json

{
    "refreshToken": "string"
}
```

响应：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "token": "string",
        "refreshToken": "string",
        "expiresIn": 3600
    }
}
```

## 4. 用户管理

### 4.1 获取用户列表
```http
GET /users
Query Parameters:
- page: 页码（默认1）
- pageSize: 每页数量（默认20）
- keyword: 搜索关键词
```

响应：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 100,
        "items": [
            {
                "id": "string",
                "username": "string",
                "email": "string",
                "role": "string",
                "status": "active",
                "createdAt": "2024-01-01T00:00:00Z"
            }
        ]
    }
}
```

### 4.2 创建用户
```http
POST /users
Content-Type: application/json

{
    "username": "string",
    "password": "string",
    "email": "string",
    "role": "string"
}
```

### 4.3 更新用户
```http
PUT /users/{id}
Content-Type: application/json

{
    "email": "string",
    "role": "string",
    "status": "string"
}
```

### 4.4 删除用户
```http
DELETE /users/{id}
```

## 5. 权限管理

### 5.1 获取权限列表
```http
GET /api/permission
```

响应：
```json
{
    "code": 200,
    "message": "获取权限列表成功",
    "data": [
        {
            "id": "guid",
            "name": "用户管理",
            "code": "USER_MANAGE",
            "description": "用户管理权限",
            "permission": 0,
            "isEnabled": true,
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z"
        }
    ]
}
```

### 5.2 获取权限详情
```http
GET /api/permission/{id}
```

### 5.3 创建权限
```http
POST /api/permission
Content-Type: application/json

{
    "name": "用户管理",
    "code": "USER_MANAGE",
    "description": "用户管理权限",
    "permission": 0
}
```

### 5.4 更新权限
```http
PUT /api/permission/{id}
Content-Type: application/json

{
    "name": "用户管理",
    "code": "USER_MANAGE",
    "description": "用户管理权限",
    "permission": 0
}
```

### 5.5 删除权限
```http
DELETE /api/permission/{id}
```

### 5.6 启用权限
```http
PATCH /api/permission/{id}/enable
```

### 5.7 禁用权限
```http
PATCH /api/permission/{id}/disable
```

## 6. 角色管理

### 6.1 获取角色列表
```http
GET /api/role
```

响应：
```json
{
    "code": 200,
    "message": "获取角色列表成功",
    "data": [
        {
            "id": "guid",
            "name": "管理员",
            "code": "ADMIN",
            "roleType": 0,
            "description": "系统管理员",
            "isActive": true,
            "permissionCodes": "USER_MANAGE,ROLE_MANAGE",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z"
        }
    ]
}
```

### 6.2 获取角色详情
```http
GET /api/role/{id}
```

### 6.3 创建角色
```http
POST /api/role
Content-Type: application/json

{
    "name": "管理员",
    "code": "ADMIN",
    "description": "系统管理员",
    "role": 0
}
```

### 6.4 更新角色
```http
PUT /api/role/{id}
Content-Type: application/json

{
    "name": "管理员",
    "code": "ADMIN",
    "description": "系统管理员",
    "role": 0
}
```

### 6.5 删除角色
```http
DELETE /api/role/{id}
```

### 6.6 启用角色
```http
PATCH /api/role/{id}/enable
```

### 6.7 禁用角色
```http
PATCH /api/role/{id}/disable
```

### 6.8 为角色分配权限
```http
POST /api/role/{id}/permissions/{permissionCode}
```

### 6.9 移除角色权限
```http
DELETE /api/role/{id}/permissions/{permissionCode}
```

## 7. 用户管理

### 7.1 获取用户列表
```http
GET /api/user
```

响应：
```json
{
    "code": 200,
    "message": "获取用户列表成功",
    "data": [
        {
            "id": "guid",
            "username": "admin",
            "nickname": "管理员",
            "avatarUrl": "https://example.com/avatar.jpg",
            "isEnabled": true,
            "role": 0,
            "permission": 0,
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z"
        }
    ]
}
```

### 7.2 获取用户详情
```http
GET /api/user/{id}
```

### 7.3 创建用户
```http
POST /api/user
Content-Type: application/json

{
    "username": "testuser",
    "password": "123456",
    "nickname": "测试用户",
    "avatarUrl": "https://example.com/avatar.jpg"
}
```

### 7.4 更新用户
```http
PUT /api/user/{id}
Content-Type: application/json

{
    "nickname": "新昵称",
    "avatarUrl": "https://example.com/new-avatar.jpg"
}
```

### 7.5 删除用户
```http
DELETE /api/user/{id}
```

### 7.6 禁用用户
```http
PATCH /api/user/{id}/disable
```

### 7.7 为用户分配角色
```http
POST /api/user/{userId}/roles/{roleId}
```

### 7.8 移除用户角色
```http
DELETE /api/user/{userId}/roles/{roleId}
```

### 7.9 更新用户头像
```http
PATCH /api/user/{id}/avatar
Content-Type: application/json

"https://example.com/new-avatar.jpg"
```

### 7.10 更新用户昵称
```http
PATCH /api/user/{id}/nickname
Content-Type: application/json

"新昵称"
```

### 7.11 修改用户密码
```http
PATCH /api/user/{id}/password
Content-Type: application/json

{
    "oldPassword": "旧密码",
    "newPassword": "新密码"
}
```

## 8. 数据字典

### 8.1 权限级别枚举
```json
{
    "All": 0,        // 全部权限 - 可以访问所有数据
    "Department": 1, // 部门权限 - 只能访问本部门数据
    "Self": 2        // 个人权限 - 只能访问自己的数据
}
```

### 8.2 角色类型枚举
```json
{
    "Admin": 0,  // 管理员角色
    "User": 1    // 普通用户角色
}
```

## 9. 文件上传

### 9.1 上传文件
```http
POST /roles
Content-Type: application/json

{
    "name": "string",
    "description": "string",
    "permissions": ["string"]
}
```

### 5.3 更新角色
```http
PUT /roles/{id}
Content-Type: application/json

{
    "name": "string",
    "description": "string",
    "permissions": ["string"]
}
```

### 5.4 删除角色
```http
DELETE /roles/{id}
```

## 6. 系统配置

### 6.1 获取配置列表
```http
GET /settings
```

### 6.2 更新配置
```http
PUT /settings/{key}
Content-Type: application/json

{
    "value": "string"
}
```

## 7. 日志管理

### 7.1 获取操作日志
```http
GET /logs/operations
Query Parameters:
- page: 页码
- pageSize: 每页数量
- startDate: 开始日期
- endDate: 结束日期
- type: 日志类型
```

### 7.2 获取系统日志
```http
GET /logs/system
Query Parameters:
- page: 页码
- pageSize: 每页数量
- level: 日志级别
- startDate: 开始日期
- endDate: 结束日期
```

## 8. 文件上传

### 8.1 上传文件
```http
POST /files/upload
Content-Type: multipart/form-data

file: (binary)
```

响应：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "url": "string",
        "filename": "string",
        "size": 1024
    }
}
```

### 8.2 删除文件
```http
DELETE /files/{id}
```

## 9. 数据字典

### 9.1 获取字典列表
```http
GET /dictionaries
```

### 9.2 获取字典项
```http
GET /dictionaries/{code}/items
```

## 10. 错误处理

### 10.1 错误响应格式
```json
{
    "code": 400,
    "message": "错误信息",
    "errors": [
        "用户名不能为空",
        "密码长度必须在6-100个字符之间"
    ]
}
```

### 10.2 常见错误码
- **200**: 操作成功
- **400**: 参数验证失败
- **401**: 未授权访问
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器内部错误

### 10.3 业务错误码
- **3001**: 权限不存在
- **3002**: 权限已存在
- **40401**: 资源未找到
- **40301**: 用户已禁用
- **40901**: 数据重复

### 10.4 验证错误示例
```json
{
    "code": 400,
    "message": "参数验证失败",
    "errors": [
        "权限名称不能为空",
        "权限代码只能包含大写字母和下划线",
        "权限描述长度不能超过200个字符"
    ]
}
```

## 11. 更新历史

### v1.1.0 (2025-01-10)
- 完善权限管理API接口
- 新增角色管理API接口
- 新增用户管理API接口
- 修复命名空间不一致问题
- 添加完整的DTO验证
- 改进错误处理和响应格式
- 添加Swagger文档支持

### v1.0.0 (2024-01)
- 初始API文档
- 基础接口定义
- 认证授权接口