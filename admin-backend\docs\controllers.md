# Controller 层设计文档

## 概述

本文档描述了BackStagePro项目中Controller层的设计和实现。Controller层负责处理HTTP请求，调用业务服务，并返回适当的响应。

## 设计原则

### 1. 职责单一
每个Controller只负责一个业务领域的API接口，如用户管理、角色管理、权限管理等。

### 2. 统一响应格式
所有API接口都返回统一的响应格式：
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {}
}
```

### 3. 完整的错误处理
- 参数验证错误处理
- 业务异常处理
- 系统异常处理
- 详细的日志记录

### 4. RESTful设计
遵循RESTful API设计规范：
- GET: 查询操作
- POST: 创建操作
- PUT: 更新操作
- DELETE: 删除操作
- PATCH: 部分更新操作

## Controller 列表

### 1. PermissionController (权限管理)

#### 功能描述
负责权限的CRUD操作和状态管理。

#### 主要接口
- `GET /api/permission` - 获取权限列表
- `GET /api/permission/{id}` - 获取权限详情
- `POST /api/permission` - 创建权限
- `PUT /api/permission/{id}` - 更新权限
- `DELETE /api/permission/{id}` - 删除权限
- `PATCH /api/permission/{id}/enable` - 启用权限
- `PATCH /api/permission/{id}/disable` - 禁用权限

#### 特性
- 完整的参数验证
- 统一的错误处理
- 详细的日志记录
- Swagger文档注释

### 2. RoleController (角色管理)

#### 功能描述
负责角色的CRUD操作、状态管理和权限分配。

#### 主要接口
- `GET /api/role` - 获取角色列表
- `GET /api/role/{id}` - 获取角色详情
- `POST /api/role` - 创建角色
- `PUT /api/role/{id}` - 更新角色
- `DELETE /api/role/{id}` - 删除角色
- `PATCH /api/role/{id}/enable` - 启用角色
- `PATCH /api/role/{id}/disable` - 禁用角色
- `POST /api/role/{id}/permissions/{permissionCode}` - 分配权限
- `DELETE /api/role/{id}/permissions/{permissionCode}` - 移除权限

#### 特性
- 支持角色权限管理
- 权限代码验证
- 角色状态管理

### 3. UserController (用户管理)

#### 功能描述
负责用户的CRUD操作、状态管理、角色分配和个人信息管理。

#### 主要接口
- `GET /api/user` - 获取用户列表
- `GET /api/user/{id}` - 获取用户详情
- `POST /api/user` - 创建用户
- `PUT /api/user/{id}` - 更新用户
- `DELETE /api/user/{id}` - 删除用户
- `PATCH /api/user/{id}/disable` - 禁用用户
- `POST /api/user/{userId}/roles/{roleId}` - 分配角色
- `DELETE /api/user/{userId}/roles/{roleId}` - 移除角色
- `PATCH /api/user/{id}/avatar` - 更新头像
- `PATCH /api/user/{id}/nickname` - 更新昵称
- `PATCH /api/user/{id}/password` - 修改密码

#### 特性
- 支持用户角色管理
- 个人信息管理
- 密码安全处理
- 头像和昵称更新

## 通用特性

### 1. 参数验证
所有Controller都实现了完整的参数验证：
```csharp
if (!ModelState.IsValid)
{
    return BadRequest(new { 
        code = 400, 
        message = "参数验证失败", 
        errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage) 
    });
}
```

### 2. 异常处理
统一的异常处理模式：
```csharp
try
{
    // 业务逻辑
    var result = await _service.SomeMethodAsync();
    return Ok(result);
}
catch (Exception ex)
{
    _logger.LogError(ex, "操作失败");
    return StatusCode(500, new { code = 500, message = "服务器内部错误" });
}
```

### 3. 日志记录
详细的日志记录，包括：
- 操作参数
- 异常信息
- 关键业务数据

### 4. Swagger文档
所有Controller和Action都添加了XML注释，支持自动生成API文档。

## 依赖注入

所有Controller都通过构造函数注入依赖：
```csharp
public class PermissionController : ControllerBase
{
    private readonly IAppPermissionService _permissionService;
    private readonly ILogger<PermissionController> _logger;

    public PermissionController(IAppPermissionService permissionService, ILogger<PermissionController> logger)
    {
        _permissionService = permissionService;
        _logger = logger;
    }
}
```

## 响应格式标准

### 成功响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据
    }
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "参数验证失败",
    "errors": [
        "字段验证错误信息"
    ]
}
```

### 业务错误响应
```json
{
    "code": 3001,
    "message": "权限不存在"
}
```

## 安全考虑

### 1. 输入验证
- 所有输入参数都进行验证
- 使用DTO进行数据传输
- 防止SQL注入和XSS攻击

### 2. 错误信息
- 不暴露敏感的系统信息
- 提供用户友好的错误消息
- 详细错误信息记录到日志

### 3. 日志安全
- 不记录敏感信息（如密码）
- 记录操作用户和时间
- 记录关键业务操作

## 性能优化

### 1. 异步操作
所有数据库操作都使用异步方法，提高并发性能。

### 2. 参数验证
在Controller层进行参数验证，减少不必要的业务层调用。

### 3. 错误处理
快速失败原则，及早发现和处理错误。

## 扩展性

### 1. 版本控制
预留API版本控制的设计空间。

### 2. 中间件支持
支持认证、授权、限流等中间件。

### 3. 缓存支持
为查询接口预留缓存支持。

## 测试建议

### 1. 单元测试
- 测试所有Action方法
- 模拟服务依赖
- 验证响应格式

### 2. 集成测试
- 端到端API测试
- 数据库集成测试
- 中间件集成测试

### 3. 性能测试
- 并发请求测试
- 大数据量测试
- 响应时间测试
