# 数据库设计文档

## 1. 概述
本文档描述了后台管理系统的数据库设计，包括表结构、关系模型和索引设计等内容。系统使用SQL Server作为主要数据库。

## 2. 数据库配置

### 2.1 基本信息
- 数据库类型：SQL Server
- 字符集：UTF-8
- 排序规则：Chinese_PRC_CI_AS
- 版本要求：SQL Server 2019+

### 2.2 连接配置
```json
{
    "ConnectionStrings": {
        "DefaultConnection": "Server=localhost;Database=AdminDB;Trusted_Connection=True;MultipleActiveResultSets=true"
    }
}
```

## 3. 表结构设计

### 3.1 用户表 (Users)
```sql
CREATE TABLE Users (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Username NVARCHAR(50) NOT NULL,
    Password NVARCHAR(100) NOT NULL,
    Email NVARCHAR(100),
    PhoneNumber NVARCHAR(20),
    Status NVARCHAR(20) NOT NULL DEFAULT 'Active',
    LastLoginTime DATETIME,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    UpdatedBy UNIQUEIDENTIFIER,
    IsDeleted BIT NOT NULL DEFAULT 0,
    CONSTRAINT UQ_Users_Username UNIQUE (Username),
    CONSTRAINT UQ_Users_Email UNIQUE (Email)
)
```

### 3.2 角色表 (Roles)
```sql
CREATE TABLE Roles (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) NOT NULL,
    Code NVARCHAR(50) NOT NULL,
    Description NVARCHAR(200),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    UpdatedBy UNIQUEIDENTIFIER,
    IsDeleted BIT NOT NULL DEFAULT 0,
    CONSTRAINT UQ_Roles_Name UNIQUE (Name),
    CONSTRAINT UQ_Roles_Code UNIQUE (Code)
)
```

### 3.3 用户角色关联表 (UserRoles)
```sql
CREATE TABLE UserRoles (
    UserId UNIQUEIDENTIFIER NOT NULL,
    RoleId UNIQUEIDENTIFIER NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    CONSTRAINT PK_UserRoles PRIMARY KEY (UserId, RoleId),
    CONSTRAINT FK_UserRoles_Users FOREIGN KEY (UserId) REFERENCES Users(Id),
    CONSTRAINT FK_UserRoles_Roles FOREIGN KEY (RoleId) REFERENCES Roles(Id)
)
```

### 3.4 权限表 (Permissions)
```sql
CREATE TABLE Permissions (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) NOT NULL,
    Code NVARCHAR(50) NOT NULL,
    Description NVARCHAR(200),
    Type NVARCHAR(20) NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    UpdatedBy UNIQUEIDENTIFIER,
    IsDeleted BIT NOT NULL DEFAULT 0,
    CONSTRAINT UQ_Permissions_Code UNIQUE (Code)
)
```

### 3.5 角色权限关联表 (RolePermissions)
```sql
CREATE TABLE RolePermissions (
    RoleId UNIQUEIDENTIFIER NOT NULL,
    PermissionId UNIQUEIDENTIFIER NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    CONSTRAINT PK_RolePermissions PRIMARY KEY (RoleId, PermissionId),
    CONSTRAINT FK_RolePermissions_Roles FOREIGN KEY (RoleId) REFERENCES Roles(Id),
    CONSTRAINT FK_RolePermissions_Permissions FOREIGN KEY (PermissionId) REFERENCES Permissions(Id)
)
```

### 3.6 操作日志表 (OperationLogs)
```sql
CREATE TABLE OperationLogs (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserId UNIQUEIDENTIFIER,
    Type NVARCHAR(50) NOT NULL,
    Action NVARCHAR(50) NOT NULL,
    Description NVARCHAR(500),
    IP NVARCHAR(50),
    UserAgent NVARCHAR(500),
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_OperationLogs_Users FOREIGN KEY (UserId) REFERENCES Users(Id)
)
```

### 3.7 系统配置表 (Settings)
```sql
CREATE TABLE Settings (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Key NVARCHAR(50) NOT NULL,
    Value NVARCHAR(MAX) NOT NULL,
    Description NVARCHAR(200),
    Type NVARCHAR(20) NOT NULL DEFAULT 'String',
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    UpdatedBy UNIQUEIDENTIFIER,
    CONSTRAINT UQ_Settings_Key UNIQUE (Key)
)
```

## 4. 索引设计

### 4.1 Users表索引
```sql
CREATE INDEX IX_Users_Email ON Users(Email) WHERE Email IS NOT NULL
CREATE INDEX IX_Users_Status ON Users(Status)
CREATE INDEX IX_Users_CreatedAt ON Users(CreatedAt)
```

### 4.2 Roles表索引
```sql
CREATE INDEX IX_Roles_Code ON Roles(Code)
CREATE INDEX IX_Roles_CreatedAt ON Roles(CreatedAt)
```

### 4.3 Permissions表索引
```sql
CREATE INDEX IX_Permissions_Code ON Permissions(Code)
CREATE INDEX IX_Permissions_Type ON Permissions(Type)
```

### 4.4 OperationLogs表索引
```sql
CREATE INDEX IX_OperationLogs_UserId ON OperationLogs(UserId)
CREATE INDEX IX_OperationLogs_Type ON OperationLogs(Type)
CREATE INDEX IX_OperationLogs_CreatedAt ON OperationLogs(CreatedAt)
```

## 5. 数据库维护

### 5.1 备份策略
- 每日全量备份
- 每小时事务日志备份
- 备份文件保留30天

### 5.2 性能优化
- 定期更新统计信息
- 定期重建索引
- 监控长时间运行的查询
- 优化查询计划

### 5.3 数据清理
- 操作日志保留180天
- 软删除数据定期清理
- 临时数据定期清理

## 6. 安全策略

### 6.1 访问控制
- 最小权限原则
- 使用存储过程
- 避免使用SA账号
- 定期修改密码

### 6.2 数据加密
- 敏感数据加密存储
- 传输数据加密
- 备份文件加密

### 6.3 审计日志
- 记录数据变更
- 记录访问失败
- 记录权限变更

## 7. 数据迁移

### 7.1 迁移脚本
```sql
-- 初始化脚本
CREATE DATABASE AdminDB
GO

USE AdminDB
GO

-- 创建表结构
-- ... (上述建表语句)

-- 初始化基础数据
INSERT INTO Roles (Name, Code, Description)
VALUES ('管理员', 'ADMIN', '系统管理员'),
       ('普通用户', 'USER', '普通用户')
```

### 7.2 版本控制
- 使用EF Core迁移
- 版本号管理
- 回滚机制

## 8. 更新历史

### v1.0.0 (2024-01)
- 初始数据库设计
- 基础表结构
- 索引优化 