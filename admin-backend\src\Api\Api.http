GET    /api/user                 - 获取用户列表
GET    /api/user/{id}            - 获取用户详情
POST   /api/user                 - 创建用户
PUT    /api/user/{id}            - 更新用户
DELETE /api/user/{id}            - 删除用户
PATCH  /api/user/{id}/disable    - 禁用用户
POST   /api/user/{userId}/roles/{roleId} - 分配角色
DELETE /api/user/{userId}/roles/{roleId} - 移除角色
PATCH  /api/user/{id}/avatar     - 更新头像
PATCH  /api/user/{id}/nickname   - 更新昵称
PATCH  /api/user/{id}/password   - 修改密码