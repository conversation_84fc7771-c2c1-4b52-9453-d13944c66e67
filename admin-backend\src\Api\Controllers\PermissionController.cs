using Microsoft.AspNetCore.Mvc;
using Application.Services;
using Application.Dtos;
using System.ComponentModel.DataAnnotations;

namespace Api.Controllers;

/// <summary>
/// 权限管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class PermissionController : ControllerBase
{
    private readonly IAppPermissionService _permissionService;
    private readonly ILogger<PermissionController> _logger;

    public PermissionController(IAppPermissionService permissionService, ILogger<PermissionController> logger)
    {
        _permissionService = permissionService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有权限列表
    /// </summary>
    /// <returns>权限列表</returns>
    [HttpGet]
    public async Task<IActionResult> GetAllPermissions()
    {
        try
        {
            var result = await _permissionService.GetAllPermissionsAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限列表时发生错误");
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 根据ID获取权限详情
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <returns>权限详情</returns>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetPermissionById([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "权限ID不能为空" });
            }

            var result = await _permissionService.GetPermissionByIdAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限详情时发生错误，权限ID: {PermissionId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 创建新权限
    /// </summary>
    /// <param name="dto">创建权限DTO</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    public async Task<IActionResult> CreatePermission([FromBody] CreatePermissionDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { 
                    code = 400, 
                    message = "参数验证失败", 
                    errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage) 
                });
            }

            var result = await _permissionService.CreatePermissionAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建权限时发生错误，权限名称: {PermissionName}", dto?.Name);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 更新权限
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <param name="dto">更新权限DTO</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdatePermission([FromRoute] Guid id, [FromBody] UpdatePermissionDto dto)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "权限ID不能为空" });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(new { 
                    code = 400, 
                    message = "参数验证失败", 
                    errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage) 
                });
            }

            var result = await _permissionService.UpdatePermissionAsync(id, dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新权限时发生错误，权限ID: {PermissionId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 删除权限
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeletePermission([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "权限ID不能为空" });
            }

            var result = await _permissionService.DeletePermissionAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除权限时发生错误，权限ID: {PermissionId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 启用权限
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <returns>启用结果</returns>
    [HttpPatch("{id}/enable")]
    public async Task<IActionResult> EnablePermission([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "权限ID不能为空" });
            }

            var result = await _permissionService.EnablePermissionAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启用权限时发生错误，权限ID: {PermissionId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 禁用权限
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <returns>禁用结果</returns>
    [HttpPatch("{id}/disable")]
    public async Task<IActionResult> DisablePermission([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "权限ID不能为空" });
            }

            var result = await _permissionService.DisablePermissionAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "禁用权限时发生错误，权限ID: {PermissionId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }
}
