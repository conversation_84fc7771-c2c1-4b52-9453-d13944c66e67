using Microsoft.AspNetCore.Mvc;
using Application.Services;
using Application.Dtos;

namespace Api.Controllers;

/// <summary>
/// 角色管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class RoleController : ControllerBase
{
    private readonly IAppRoleService _roleService;
    private readonly ILogger<RoleController> _logger;

    public RoleController(IAppRoleService roleService, ILogger<RoleController> logger)
    {
        _roleService = roleService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有角色列表
    /// </summary>
    /// <returns>角色列表</returns>
    [HttpGet]
    public async Task<IActionResult> GetAllRoles()
    {
        try
        {
            var result = await _roleService.GetAllRoleAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色列表时发生错误");
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 根据ID获取角色详情
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>角色详情</returns>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetRoleById([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "角色ID不能为空" });
            }

            var result = await _roleService.GetByIdRoleAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色详情时发生错误，角色ID: {RoleId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 创建新角色
    /// </summary>
    /// <param name="dto">创建角色DTO</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    public async Task<IActionResult> CreateRole([FromBody] CreateRoleDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { 
                    code = 400, 
                    message = "参数验证失败", 
                    errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage) 
                });
            }

            var result = await _roleService.CreateRoleAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建角色时发生错误，角色名称: {RoleName}", dto?.Name);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 更新角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="dto">更新角色DTO</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateRole([FromRoute] Guid id, [FromBody] UpdateRoleDto dto)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "角色ID不能为空" });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(new { 
                    code = 400, 
                    message = "参数验证失败", 
                    errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage) 
                });
            }

            var result = await _roleService.UpdateRoleAsync(id, dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新角色时发生错误，角色ID: {RoleId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 删除角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteRole([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "角色ID不能为空" });
            }

            var result = await _roleService.DeleteRoleAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除角色时发生错误，角色ID: {RoleId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 启用角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>启用结果</returns>
    [HttpPatch("{id}/enable")]
    public async Task<IActionResult> EnableRole([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "角色ID不能为空" });
            }

            var result = await _roleService.EnableRole(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启用角色时发生错误，角色ID: {RoleId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 禁用角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>禁用结果</returns>
    [HttpPatch("{id}/disable")]
    public async Task<IActionResult> DisableRole([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "角色ID不能为空" });
            }

            var result = await _roleService.DisableRole(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "禁用角色时发生错误，角色ID: {RoleId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 为角色分配权限
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="permissionCode">权限代码</param>
    /// <returns>分配结果</returns>
    [HttpPost("{id}/permissions/{permissionCode}")]
    public async Task<IActionResult> AssignPermission([FromRoute] Guid id, [FromRoute] string permissionCode)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "角色ID不能为空" });
            }

            if (string.IsNullOrWhiteSpace(permissionCode))
            {
                return BadRequest(new { code = 400, message = "权限代码不能为空" });
            }

            var result = await _roleService.AssignPermissionAsync(id, permissionCode);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分配权限时发生错误，角色ID: {RoleId}, 权限代码: {PermissionCode}", id, permissionCode);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 移除角色权限
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="permissionCode">权限代码</param>
    /// <returns>移除结果</returns>
    [HttpDelete("{id}/permissions/{permissionCode}")]
    public async Task<IActionResult> RemovePermission([FromRoute] Guid id, [FromRoute] string permissionCode)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "角色ID不能为空" });
            }

            if (string.IsNullOrWhiteSpace(permissionCode))
            {
                return BadRequest(new { code = 400, message = "权限代码不能为空" });
            }

            var result = await _roleService.RemovePermissionAsync(id, permissionCode);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除权限时发生错误，角色ID: {RoleId}, 权限代码: {PermissionCode}", id, permissionCode);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }
}
