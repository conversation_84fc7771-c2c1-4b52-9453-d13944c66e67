using Microsoft.AspNetCore.Mvc;
using Application.Services;
using Application.Dtos;

namespace Api.Controllers;

/// <summary>
/// 用户管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class UserController : ControllerBase
{
    private readonly IAppUserService _userService;
    private readonly ILogger<UserController> _logger;

    public UserController(IAppUserService userService, ILogger<UserController> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有用户列表
    /// </summary>
    /// <returns>用户列表</returns>
    [HttpGet]
    public async Task<IActionResult> GetAllUsers()
    {
        try
        {
            var result = await _userService.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户列表时发生错误");
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 根据ID获取用户详情
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>用户详情</returns>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetUserById([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "用户ID不能为空" });
            }

            var result = await _userService.GetByIdAsync(id);
            if (result == null)
            {
                return NotFound(new { code = 404, message = "用户不存在" });
            }

            return Ok(new { code = 200, message = "获取用户详情成功", data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户详情时发生错误，用户ID: {UserId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 创建新用户
    /// </summary>
    /// <param name="dto">创建用户DTO</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    public async Task<IActionResult> CreateUser([FromBody] CreateUserDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { 
                    code = 400, 
                    message = "参数验证失败", 
                    errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage) 
                });
            }

            var result = await _userService.CreateAsync(dto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建用户时发生错误，用户名: {Username}", dto?.Username);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 更新用户信息
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="dto">更新用户DTO</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateUser([FromRoute] Guid id, [FromBody] UpdateUserDto dto)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "用户ID不能为空" });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(new { 
                    code = 400, 
                    message = "参数验证失败", 
                    errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage) 
                });
            }

            await _userService.UpdateAsync(id, dto);
            return Ok(new { code = 200, message = "用户更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户时发生错误，用户ID: {UserId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteUser([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "用户ID不能为空" });
            }

            await _userService.DeleteAsync(id);
            return Ok(new { code = 200, message = "用户删除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除用户时发生错误，用户ID: {UserId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 禁用用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>禁用结果</returns>
    [HttpPatch("{id}/disable")]
    public async Task<IActionResult> DisableUser([FromRoute] Guid id)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "用户ID不能为空" });
            }

            var result = await _userService.DisbleAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "禁用用户时发生错误，用户ID: {UserId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 为用户分配角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleId">角色ID</param>
    /// <returns>分配结果</returns>
    [HttpPost("{userId}/roles/{roleId}")]
    public async Task<IActionResult> AssignRole([FromRoute] Guid userId, [FromRoute] Guid roleId)
    {
        try
        {
            if (userId == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "用户ID不能为空" });
            }

            if (roleId == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "角色ID不能为空" });
            }

            await _userService.AssignRoleAsync(userId, roleId);
            return Ok(new { code = 200, message = "角色分配成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分配角色时发生错误，用户ID: {UserId}, 角色ID: {RoleId}", userId, roleId);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 移除用户角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleId">角色ID</param>
    /// <returns>移除结果</returns>
    [HttpDelete("{userId}/roles/{roleId}")]
    public async Task<IActionResult> RemoveRole([FromRoute] Guid userId, [FromRoute] Guid roleId)
    {
        try
        {
            if (userId == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "用户ID不能为空" });
            }

            if (roleId == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "角色ID不能为空" });
            }

            await _userService.RemoveRoleAsync(userId, roleId);
            return Ok(new { code = 200, message = "角色移除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除角色时发生错误，用户ID: {UserId}, 角色ID: {RoleId}", userId, roleId);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 更新用户头像
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="avatarUrl">头像URL</param>
    /// <returns>更新结果</returns>
    [HttpPatch("{id}/avatar")]
    public async Task<IActionResult> UpdateAvatar([FromRoute] Guid id, [FromBody] string avatarUrl)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "用户ID不能为空" });
            }

            if (string.IsNullOrWhiteSpace(avatarUrl))
            {
                return BadRequest(new { code = 400, message = "头像URL不能为空" });
            }

            await _userService.UpdateAvatarAsync(id, avatarUrl);
            return Ok(new { code = 200, message = "头像更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新头像时发生错误，用户ID: {UserId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 更新用户昵称
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="nickname">昵称</param>
    /// <returns>更新结果</returns>
    [HttpPatch("{id}/nickname")]
    public async Task<IActionResult> UpdateNickname([FromRoute] Guid id, [FromBody] string nickname)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "用户ID不能为空" });
            }

            if (string.IsNullOrWhiteSpace(nickname))
            {
                return BadRequest(new { code = 400, message = "昵称不能为空" });
            }

            await _userService.UpdateNickNameAsync(id, nickname);
            return Ok(new { code = 200, message = "昵称更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新昵称时发生错误，用户ID: {UserId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }

    /// <summary>
    /// 修改用户密码
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="request">密码修改请求</param>
    /// <returns>修改结果</returns>
    [HttpPatch("{id}/password")]
    public async Task<IActionResult> ChangePassword([FromRoute] Guid id, [FromBody] ChangePasswordRequest request)
    {
        try
        {
            if (id == Guid.Empty)
            {
                return BadRequest(new { code = 400, message = "用户ID不能为空" });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(new {
                    code = 400,
                    message = "参数验证失败",
                    errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)
                });
            }

            await _userService.ChangPasswordAsync(id, request.OldPassword, request.NewPassword);
            return Ok(new { code = 200, message = "密码修改成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修改密码时发生错误，用户ID: {UserId}", id);
            return StatusCode(500, new { code = 500, message = "服务器内部错误" });
        }
    }
}

/// <summary>
/// 修改密码请求模型
/// </summary>
public class ChangePasswordRequest
{
    /// <summary>
    /// 旧密码
    /// </summary>
    [Required(ErrorMessage = "旧密码不能为空")]
    public string OldPassword { get; set; } = null!;

    /// <summary>
    /// 新密码
    /// </summary>
    [Required(ErrorMessage = "新密码不能为空")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "新密码长度必须在6-100个字符之间")]
    public string NewPassword { get; set; } = null!;
}
