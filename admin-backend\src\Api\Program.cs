using System.Globalization;
using Microsoft.AspNetCore.Localization;
using Microsoft.EntityFrameworkCore;
using Pomelo.EntityFrameworkCore.MySql;
using Api.Services;

var builder = WebApplication.CreateBuilder(args);

// 配置本地化
builder.Services.AddLocalization(options => options.ResourcesPath = "Resources");

// 配置支持的文化
var supportedCultures = new[]
{
    new CultureInfo("zh-CN"), // 简体中文
    new CultureInfo("en-US")  // 英文
};

builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    options.DefaultRequestCulture = new RequestCulture("zh-CN");
    options.SupportedCultures = supportedCultures;
    options.SupportedUICultures = supportedCultures;

    // 配置本地化提供程序
    options.RequestCultureProviders = new List<IRequestCultureProvider>
    {
        new QueryStringRequestCultureProvider(), // 通过查询字符串 ?culture=zh-CN
        new CookieRequestCultureProvider(),      // 通过Cookie
        new AcceptLanguageHeaderRequestCultureProvider() // 通过Accept-Language头
    };
});

// 注册本地化服务
builder.Services.AddScoped<ILocalizationService, LocalizationService>();

// 添加控制器支持
builder.Services.AddControllers();

// 添加API Explorer和Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "BackStagePro API",
        Version = "v1",
        Description = "后台管理系统API文档"
    });

    // 添加XML注释支持
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// 注册数据库上下文
builder.Services.AddDbContext<Infrastructure.Data.AdminDbContext>(options =>
    options.UseMySql(builder.Configuration.GetConnectionString("DefaultConnection"),
        ServerVersion.AutoDetect(builder.Configuration.GetConnectionString("DefaultConnection")),
        b => b.MigrationsAssembly("Api")));

// 注册仓储
builder.Services.AddScoped(typeof(Domain.Repositories.IRepository<>), typeof(Infrastructure.Repositories.EFRepository<>));

// 注册应用服务
builder.Services.AddScoped<Application.Services.IAppPermissionService, Application.Services.AppPermissionService>();
builder.Services.AddScoped<Application.Services.IAppRoleService, Application.Services.AppRoleService>();
builder.Services.AddScoped<Application.Services.IAppUserService, Application.Services.AppUserService>();

// 添加CORS支持
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// 启用本地化中间件
app.UseRequestLocalization();

// 启用CORS
app.UseCors("AllowAll");

app.UseHttpsRedirection();

// 添加路由支持
app.UseRouting();

// 映射控制器路由
app.MapControllers();

var summaries = new[]
{
    "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Balmy", "Hot", "Sweltering", "Scorching"
};

app.MapGet("/weatherforecast", () =>
{
    var forecast =  Enumerable.Range(1, 5).Select(index =>
        new WeatherForecast
        (
            DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
            Random.Shared.Next(-20, 55),
            summaries[Random.Shared.Next(summaries.Length)]
        ))
        .ToArray();
    return forecast;
})
.WithName("GetWeatherForecast")
.WithOpenApi();

app.Run();

record WeatherForecast(DateOnly Date, int TemperatureC, string? Summary)
{
    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
}
