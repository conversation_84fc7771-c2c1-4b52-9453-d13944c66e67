<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Common Messages -->
  <data name="Success" xml:space="preserve">
    <value>Operation successful</value>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>Operation failed</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>Resource not found</value>
  </data>
  <data name="Unauthorized" xml:space="preserve">
    <value>Unauthorized access</value>
  </data>
  <data name="ValidationError" xml:space="preserve">
    <value>Validation failed</value>
  </data>
  
  <!-- User Related -->
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="Nickname" xml:space="preserve">
    <value>Nickname</value>
  </data>
  <data name="Avatar" xml:space="preserve">
    <value>Avatar</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="CreateTime" xml:space="preserve">
    <value>Create Time</value>
  </data>
  <data name="UpdateTime" xml:space="preserve">
    <value>Update Time</value>
  </data>
  
  <!-- Role & Permission -->
  <data name="Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="Permission" xml:space="preserve">
    <value>Permission</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="Menu" xml:space="preserve">
    <value>Menu</value>
  </data>
  
  <!-- Actions -->
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  
  <!-- Validation Messages -->
  <data name="Required" xml:space="preserve">
    <value>{0} is required</value>
  </data>
  <data name="MinLength" xml:space="preserve">
    <value>{0} must be at least {1} characters</value>
  </data>
  <data name="MaxLength" xml:space="preserve">
    <value>{0} cannot exceed {1} characters</value>
  </data>
  <data name="EmailFormat" xml:space="preserve">
    <value>Invalid email format</value>
  </data>
  <data name="PhoneFormat" xml:space="preserve">
    <value>Invalid phone format</value>
  </data>
  
  <!-- Login Related -->
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
  <data name="LoginSuccess" xml:space="preserve">
    <value>Login successful</value>
  </data>
  <data name="LoginFailed" xml:space="preserve">
    <value>Invalid username or password</value>
  </data>
  <data name="TokenExpired" xml:space="preserve">
    <value>Session expired, please login again</value>
  </data>
  
</root>
