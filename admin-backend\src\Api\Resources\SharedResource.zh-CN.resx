<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- 通用消息 -->
  <data name="Success" xml:space="preserve">
    <value>操作成功</value>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>操作失败</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>未找到资源</value>
  </data>
  <data name="Unauthorized" xml:space="preserve">
    <value>未授权访问</value>
  </data>
  <data name="ValidationError" xml:space="preserve">
    <value>验证失败</value>
  </data>
  
  <!-- 用户相关 -->
  <data name="User" xml:space="preserve">
    <value>用户</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>邮箱</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>电话</value>
  </data>
  <data name="Nickname" xml:space="preserve">
    <value>昵称</value>
  </data>
  <data name="Avatar" xml:space="preserve">
    <value>头像</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="CreateTime" xml:space="preserve">
    <value>创建时间</value>
  </data>
  <data name="UpdateTime" xml:space="preserve">
    <value>更新时间</value>
  </data>
  
  <!-- 角色权限 -->
  <data name="Role" xml:space="preserve">
    <value>角色</value>
  </data>
  <data name="Permission" xml:space="preserve">
    <value>权限</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>部门</value>
  </data>
  <data name="Menu" xml:space="preserve">
    <value>菜单</value>
  </data>
  
  <!-- 操作 -->
  <data name="Add" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>确认</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>搜索</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>重置</value>
  </data>
  
  <!-- 验证消息 -->
  <data name="Required" xml:space="preserve">
    <value>{0}不能为空</value>
  </data>
  <data name="MinLength" xml:space="preserve">
    <value>{0}长度不能少于{1}个字符</value>
  </data>
  <data name="MaxLength" xml:space="preserve">
    <value>{0}长度不能超过{1}个字符</value>
  </data>
  <data name="EmailFormat" xml:space="preserve">
    <value>邮箱格式不正确</value>
  </data>
  <data name="PhoneFormat" xml:space="preserve">
    <value>手机号格式不正确</value>
  </data>
  
  <!-- 登录相关 -->
  <data name="Login" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>退出登录</value>
  </data>
  <data name="LoginSuccess" xml:space="preserve">
    <value>登录成功</value>
  </data>
  <data name="LoginFailed" xml:space="preserve">
    <value>用户名或密码错误</value>
  </data>
  <data name="TokenExpired" xml:space="preserve">
    <value>登录已过期，请重新登录</value>
  </data>
  
</root>
