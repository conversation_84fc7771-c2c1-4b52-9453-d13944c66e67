using Microsoft.Extensions.Localization;
using Api.Resources;

namespace Api.Services;

/// <summary>
/// 本地化服务
/// </summary>
public interface ILocalizationService
{
    /// <summary>
    /// 获取本地化字符串
    /// </summary>
    /// <param name="key">资源键</param>
    /// <param name="args">格式化参数</param>
    /// <returns>本地化字符串</returns>
    string GetString(string key, params object[] args);
    
    /// <summary>
    /// 获取本地化字符串（可为空）
    /// </summary>
    /// <param name="key">资源键</param>
    /// <param name="args">格式化参数</param>
    /// <returns>本地化字符串或null</returns>
    string? GetStringOrNull(string key, params object[] args);
}

/// <summary>
/// 本地化服务实现
/// </summary>
public class LocalizationService : ILocalizationService
{
    private readonly IStringLocalizer<SharedResource> _localizer;

    public LocalizationService(IStringLocalizer<SharedResource> localizer)
    {
        _localizer = localizer;
    }

    public string GetString(string key, params object[] args)
    {
        var localizedString = _localizer[key, args];
        return localizedString.ResourceNotFound ? key : localizedString.Value;
    }

    public string? GetStringOrNull(string key, params object[] args)
    {
        var localizedString = _localizer[key, args];
        return localizedString.ResourceNotFound ? null : localizedString.Value;
    }
}

/// <summary>
/// 本地化扩展方法
/// </summary>
public static class LocalizationExtensions
{
    /// <summary>
    /// 获取本地化字符串的扩展方法
    /// </summary>
    /// <param name="localizer">本地化器</param>
    /// <param name="key">资源键</param>
    /// <param name="args">格式化参数</param>
    /// <returns>本地化字符串</returns>
    public static string L(this IStringLocalizer localizer, string key, params object[] args)
    {
        var localizedString = localizer[key, args];
        return localizedString.ResourceNotFound ? key : localizedString.Value;
    }
}
