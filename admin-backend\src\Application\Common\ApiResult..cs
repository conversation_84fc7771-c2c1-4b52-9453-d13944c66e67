
namespace Application.Common;

public class ApiResult
{
    public int Code { get; set; }
    public string Message { get; set; } = "";
    public object? Data { get; set; }

    public static ApiResult Success(object? data = null, string? message = null) => new ApiResult
    {
        Code = 200,
        Message = message ?? "操作成功",
        Data = data
    };

    public static ApiResult Success(int code, string? message = null) => new ApiResult
    {
        Code = code,
        Message = message ?? "操作成功"
    };

    public static ApiResult Fail(int code, string? message = null) => new ApiResult
    {
        Code = code,
        Message = message ?? "操作失败"
    };

    public static ApiResult Fail(object? data = null, string? message = null) => new ApiResult
    {
        Code = 400,
        Message = message ?? "操作失败",
        Data = data
    };
}