using System.ComponentModel.DataAnnotations;

namespace Application.Dtos;

public class CreateUserDto
{
    [Required(ErrorMessage = "用户名不能为空")]
    [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
    public string Username { get; set; } = null!;

    [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
    public string? Password { get; set; }

    [StringLength(50, ErrorMessage = "用户代码长度不能超过50个字符")]
    public string? Code { get; set; }

    [StringLength(50, ErrorMessage = "昵称长度不能超过50个字符")]
    public string? Nickname { get; set; }

    [Url(ErrorMessage = "头像URL格式不正确")]
    public string? AvatarUrl { get; set; }
}