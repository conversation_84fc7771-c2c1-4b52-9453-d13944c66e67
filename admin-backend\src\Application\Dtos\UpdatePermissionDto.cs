using System.ComponentModel.DataAnnotations;
using Domain.Enums;

namespace Application.Dtos;

public class UpdatePermissionDto
{
    [Required(ErrorMessage = "权限名称不能为空")]
    [StringLength(50, ErrorMessage = "权限名称长度不能超过50个字符")]
    public string Name { get; set; } = null!;

    [Required(ErrorMessage = "权限代码不能为空")]
    [StringLength(50, ErrorMessage = "权限代码长度不能超过50个字符")]
    [RegularExpression(@"^[A-Z_]+$", ErrorMessage = "权限代码只能包含大写字母和下划线")]
    public string Code { get; set; } = null!;

    [StringLength(200, ErrorMessage = "权限描述长度不能超过200个字符")]
    public string? Description { get; set; }

    [Required(ErrorMessage = "权限级别不能为空")]
    public Permission Permission { get; set; }
}