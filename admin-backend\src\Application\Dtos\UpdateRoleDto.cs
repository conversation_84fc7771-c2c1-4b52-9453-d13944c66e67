using System.ComponentModel.DataAnnotations;
using Domain.Enums;

namespace Application.Dtos;

public class UpdateRoleDto
{
    [Required(ErrorMessage = "角色名称不能为空")]
    [StringLength(50, ErrorMessage = "角色名称长度不能超过50个字符")]
    public string Name { get; set; } = null!;

    [Required(ErrorMessage = "角色代码不能为空")]
    [StringLength(50, ErrorMessage = "角色代码长度不能超过50个字符")]
    [RegularExpression(@"^[A-Z_]+$", ErrorMessage = "角色代码只能包含大写字母和下划线")]
    public string Code { get; set; } = null!;

    [StringLength(200, ErrorMessage = "角色描述长度不能超过200个字符")]
    public string? Description { get; set; }

    [Required(ErrorMessage = "角色类型不能为空")]
    public Role Role { get; set; }
}