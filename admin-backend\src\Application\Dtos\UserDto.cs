using Domain.Enums;

namespace Application.Dtos;

public class UserDto
{
    public Guid Id { get; set; }
    public string Username { get; set; } = null!;
    public string? Nickname { get; set; }
    public string? AvatarUrl { get; set; }
    public bool IsEnabled { get; set; }
    public Role Role { get; set; }
    public Permission Permission { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}