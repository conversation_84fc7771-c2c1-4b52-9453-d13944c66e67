using Application.Common;
using Application.Dtos;
using Domain.Entities.App;
using Domain.Repositories;

namespace Application.Services;

public class AppDepartmentService
{
    private readonly IRepository<Department> _departmentRepository;

    public AppDepartmentService(IRepository<Department> departmentRepository)
    {
        _departmentRepository = departmentRepository;
    }
    public async Task<dynamic> DeleteDepartmentAsync(Guid id)
    {
        var department = await _departmentRepository.GetByIdAsync(id);
        if (department == null || department.IsDelete)
        {
            return ApiResult.Fail(ErrorCodes.NotFound, "部门不存在");
        }
        department.Deactivate();
        await _departmentRepository.UpdateAsync(department);
        return ApiResult.Success(new { Id = department.Id, department.Name, department.Code }, "部门删除成功");
        
        
    }

    public async Task<dynamic> EnableDepartment(Guid DepartmentId)
    {
        var department = await _departmentRepository.GetByIdAsync(DepartmentId);
        if (department == null || department.IsDelete)
        {
            return ApiResult.Fail(ErrorCodes.NotFound, "部门不存在");
        }
        department.Activate();
        await _departmentRepository.UpdateAsync(department);
        return ApiResult.Success(new { Id = department.Id, department.Name, department.Code }, "部门启用成功");
    }

    public async Task<dynamic> GetAllDepartmentAsync()
    {
     var department = await _departmentRepository.GetallAsync();
        return ApiResult.Success(department, "获取部门列表成功");
    }

    public async Task<dynamic> GetByIdDepartmentAsync(Guid id)
    {
        var department = await _departmentRepository.GetByIdAsync(id);
        if (department == null || department.IsDelete)
        {
            return ApiResult.Fail(ErrorCodes.NotFound, "部门不存在");
        }
         return ApiResult.Success(department, "获取部门成功");
   }

    public async Task<dynamic> UpdateDepartmentAsync(Guid id, UpdateDepartmentDto dto)
    {
        var department = await _departmentRepository.GetByIdAsync(id);
        if (department == null || department.IsDelete)
        {
            return ApiResult.Fail(ErrorCodes.NotFound, "部门不存在");
        }
        dto.Id = department.Id;
    var res=await _departmentRepository.UpdateAsync(department);
        return ApiResult.Success(res, "更新部门成功");
    }

    public async Task<dynamic> CreateDepartmentAsync(CreateDepartmentDto dto)
    {
        var department = await _departmentRepository.GetByIdAsync(dto.Id);
                if (department != null)
                {
                    return ApiResult.Fail(ErrorCodes.NotFound, "部门已存在");
                }
                var newDepartment = new Department(dto.DepartmentName, dto.DepartmentName)
                {
                    Id = Guid.NewGuid(),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    IsActive = true
                };
                var result = await _departmentRepository.CreateAsync(newDepartment);
                return ApiResult.Success(result, "创建部门成功");

    }
}