using Application.Common;
using Application.Dtos;
using Domain.Entities.App;
using Domain.Repositories;
using Domain.Enums;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Application.Services;

public class AppPermissionService : IAppPermissionService
{
    private readonly IRepository<AppPermission> _permissionRepository;

    public AppPermissionService(IRepository<AppPermission> permissionRepository)
    {
        _permissionRepository = permissionRepository;
    }

    public async Task<dynamic> CreatePermissionAsync(CreatePermissionDto dto)
    {
        var permissions = await _permissionRepository.GetAllAsync();
        if (permissions.Any(p => p.Name == dto.Name || p.Code == dto.Code))
        {
            return ApiResult.Fail(3002, "权限已存在");
        }
        var permission = new AppPermission(dto.Name, dto.Code, dto.Description, dto.Permission);
        var result = await _permissionRepository.CreateAsync(permission);
        return ApiResult.Success(new { Id = result.Id, result.Name, result.IsEnabled });
    }

    public async Task<dynamic> DeletePermissionAsync(Guid permissionId)
    {
        var permission = await _permissionRepository.GetByIdAsync(permissionId);
        if (permission == null)
        {
            return ApiResult.Fail(3001, "权限不存在");
        }
        await _permissionRepository.DeleteAsync(permission);
        return ApiResult.Success(new { Id = permission.Id, permission.Name, permission.IsEnabled });
    }

    public async Task<dynamic> DisablePermissionAsync(Guid permissionId)
    {
        var permission = await _permissionRepository.GetByIdAsync(permissionId);
        if (permission == null)
        {
            return ApiResult.Fail(3001, "权限不存在");
        }
        permission.IsEnabled = false;
        permission.UpdatedAt = DateTime.UtcNow;
        await _permissionRepository.UpdateAsync(permission);
        return ApiResult.Success(new { Id = permission.Id, permission.Name, permission.IsEnabled }, "禁用成功");
    }

    public async Task<dynamic> EnablePermissionAsync(Guid permissionId)
    {
        var permission = await _permissionRepository.GetByIdAsync(permissionId);
        if (permission == null)
        {
            return ApiResult.Fail(3001, "权限不存在");
        }
        permission.IsEnabled = true;
        permission.UpdatedAt = DateTime.UtcNow;
        var result = await _permissionRepository.UpdateAsync(permission);
        return ApiResult.Success(new { Id = result.Id, result.Name, result.IsEnabled }, "启用成功");
    }

    public async Task<dynamic> GetAllPermissionsAsync()
    {
        try
        {
            var permissions = await _permissionRepository.GetAllAsync();
            var permissionList = permissions.Where(p => !p.IsDelete).Select(p => new
            {
                Id = p.Id,
                Name = p.Name,
                Code = p.Code,
                Description = p.Description,
                Permission = p.Permission,
                IsEnabled = p.IsEnabled,
                CreatedAt = p.CreatedAt,
                UpdatedAt = p.UpdatedAt
            }).ToList();

            return ApiResult.Success(permissionList, "获取权限列表成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"获取权限列表失败: {ex.Message}");
        }
    }

    public async Task<dynamic> GetPermissionByIdAsync(Guid permissionId)
    {
        try
        {
            var permission = await _permissionRepository.GetByIdAsync(permissionId);
            if (permission == null || permission.IsDelete)
            {
                return ApiResult.Fail(3001, "权限不存在");
            }

            var permissionDto = new
            {
                Id = permission.Id,
                Name = permission.Name,
                Code = permission.Code,
                Description = permission.Description,
                Permission = permission.Permission,
                IsEnabled = permission.IsEnabled,
                CreatedAt = permission.CreatedAt,
                UpdatedAt = permission.UpdatedAt
            };

            return ApiResult.Success(permissionDto, "获取权限详情成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"获取权限详情失败: {ex.Message}");
        }
    }

    public async Task<dynamic> UpdatePermissionAsync(Guid permissionId, UpdatePermissionDto dto)
    {
        var permission = await _permissionRepository.GetByIdAsync(permissionId);
        if (permission == null)
        {
            return ApiResult.Fail(3001, "权限不存在");
        }
        var permissions = await _permissionRepository.GetAllAsync();
        if (permissions.Any(p => (p.Name == dto.Name || p.Code == dto.Code) && p.Id != permissionId))
        {
            return ApiResult.Fail(3002, "权限已存在");
        }
        permission.Name = dto.Name;
        permission.Code = dto.Code;
        permission.Description = dto.Description;
        permission.Permission = dto.Permission;
        permission.UpdatedAt = DateTime.UtcNow;
        await _permissionRepository.UpdateAsync(permission);
        return ApiResult.Success(1000, "更新成功");
    }
}