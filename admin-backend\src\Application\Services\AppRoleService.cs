using System;
using System.Linq;
using System.Threading.Tasks;
using Application.Common;
using Application.Dtos;
using Domain.Entities.App;
using Domain.Enums;
using Domain.Repositories;

namespace Application.Services;

public class AppRoleService : IAppRoleService
{
    private readonly IRepository<AppRole> _roleRepository;
    private readonly IRepository<AppPermission> _permissionRepository;

    public AppRoleService(
        IRepository<AppRole> roleRepository,
        IRepository<AppPermission> permissionRepository)
    {
        _roleRepository = roleRepository;
        _permissionRepository = permissionRepository;
    }

    public async Task<dynamic> AssignPermissionAsync(Guid roleId, string permissionCode)
    {
        try
        {
            // 验证角色是否存在
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null || role.IsDelete)
            {
                return ApiResult.Fail(ErrorCodes.NotFound, "角色不存在");
            }

            // 验证权限代码是否存在
            var permissions = await _permissionRepository.GetAllAsync();
            var permission = permissions.FirstOrDefault(p => p.Code == permissionCode && !p.IsDelete);
            if (permission == null)
            {
                return ApiResult.Fail(ErrorCodes.NotFound, "权限不存在");
            }

            // 检查是否已经分配过该权限
            if (role.HasPermission(permissionCode))
            {
                return ApiResult.Fail(ErrorCodes.DuplicateDisabled, "该权限已分配给此角色");
            }

            // 分配权限
            role.AssignPermission(permissionCode);
            await _roleRepository.UpdateAsync(role);

            return ApiResult.Success(new {
                RoleId = roleId,
                PermissionCode = permissionCode,
                PermissionName = permission.Name
            }, "权限分配成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"分配权限失败: {ex.Message}");
        }
    }

    public async Task<dynamic> CreateRoleAsync(CreateRoleDto dto)
    {
        try
        {
            // 验证角色名称和代码是否已存在
            var existingRoles = await _roleRepository.GetAllAsync();
            if (existingRoles.Any(r => (r.Name == dto.Name || r.Code == dto.Code) && !r.IsDelete))
            {
                return ApiResult.Fail(ErrorCodes.DuplicateDisabled, "角色名称或代码已存在");
            }

            // 创建新角色
            var newRole = new AppRole(dto.Name, dto.Code, dto.Role, dto.Description);

            var result = await _roleRepository.CreateAsync(newRole);

            return ApiResult.Success(new {
                Id = result.Id,
                Name = result.Name,
                Code = result.Code,
                RoleType = result.RoleType,
                Description = result.Description
            }, "角色创建成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"创建角色失败: {ex.Message}");
        }
    }

    public async Task<dynamic> DeleteRoleAsync(Guid id)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(id);
            if (role == null || role.IsDelete)
            {
                return ApiResult.Fail(ErrorCodes.NotFound, "角色不存在");
            }

            role.IsDelete = true;
            role.UpdatedAt = DateTime.UtcNow;
            await _roleRepository.UpdateAsync(role);

            return ApiResult.Success(new {
                Id = role.Id,
                Name = role.Name,
                Code = role.Code,
                RoleType = role.RoleType
            }, "角色删除成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"删除角色失败: {ex.Message}");
        }
    }

    public async Task<dynamic> DisableRole(Guid roleId)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null || role.IsDelete)
            {
                return ApiResult.Fail(ErrorCodes.NotFound, "角色不存在");
            }

            role.Deactivate();
            await _roleRepository.UpdateAsync(role);

            return ApiResult.Success(new {
                Id = role.Id,
                Name = role.Name,
                Code = role.Code,
                RoleType = role.RoleType
            }, "角色禁用成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"禁用角色失败: {ex.Message}");
        }
    }

    public async Task<dynamic> EnableRole(Guid roleId)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null || role.IsDelete)
            {
                return ApiResult.Fail(ErrorCodes.NotFound, "角色不存在");
            }

            role.Activate();
            await _roleRepository.UpdateAsync(role);

            return ApiResult.Success(new {
                Id = role.Id,
                Name = role.Name,
                Code = role.Code,
                RoleType = role.RoleType
            }, "角色启用成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"启用角色失败: {ex.Message}");
        }
    }

    public async Task<dynamic> GetAllRoleAsync()
    {
        try
        {
            var roles = await _roleRepository.GetAllAsync();
            var roleList = roles.Where(r => !r.IsDelete).Select(r => new
            {
                Id = r.Id,
                Name = r.Name,
                Code = r.Code,
                RoleType = r.RoleType,
                Description = r.Description,
                IsActive = r.IsActive,
                PermissionCodes = r.PermissionCodes,
                CreatedAt = r.CreatedAt,
                UpdatedAt = r.UpdatedAt
            }).ToList();

            return ApiResult.Success(roleList, "获取角色列表成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"获取角色列表失败: {ex.Message}");
        }
    }

    public async Task<dynamic> GetByIdRoleAsync(Guid id)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(id);
            if (role == null || role.IsDelete)
            {
                return ApiResult.Fail(ErrorCodes.NotFound, "角色不存在");
            }

            var roleDto = new
            {
                Id = role.Id,
                Name = role.Name,
                Code = role.Code,
                RoleType = role.RoleType,
                Description = role.Description,
                IsActive = role.IsActive,
                PermissionCodes = role.PermissionCodes,
                CreatedAt = role.CreatedAt,
                UpdatedAt = role.UpdatedAt
            };

            return ApiResult.Success(roleDto, "获取角色详情成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"获取角色详情失败: {ex.Message}");
        }
    }

    public async Task<dynamic> RemovePermissionAsync(Guid roleId, string permissionCode)
    {
        try
        {
            // 验证角色是否存在
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null || role.IsDelete)
            {
                return ApiResult.Fail(ErrorCodes.NotFound, "角色不存在");
            }

            // 验证权限代码是否存在
            var permissions = await _permissionRepository.GetAllAsync();
            var permission = permissions.FirstOrDefault(p => p.Code == permissionCode && !p.IsDelete);
            if (permission == null)
            {
                return ApiResult.Fail(ErrorCodes.NotFound, "权限不存在");
            }

            // 检查角色是否有该权限
            if (!role.HasPermission(permissionCode))
            {
                return ApiResult.Fail(ErrorCodes.NotFound, "该角色未分配此权限");
            }

            // 移除权限
            role.RemovePermission(permissionCode);
            await _roleRepository.UpdateAsync(role);

            return ApiResult.Success(new {
                RoleId = roleId,
                PermissionCode = permissionCode,
                PermissionName = permission.Name
            }, "权限移除成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"移除权限失败: {ex.Message}");
        }
    }

    public async Task<dynamic> UpdateRoleAsync(Guid id, UpdateRoleDto dto)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(id);
            if (role == null || role.IsDelete)
            {
                return ApiResult.Fail(ErrorCodes.NotFound, "角色不存在");
            }

            // 验证角色名称和代码是否已被其他角色使用
            var existingRoles = await _roleRepository.GetAllAsync();
            if (existingRoles.Any(r => (r.Name == dto.Name || r.Code == dto.Code) && r.Id != id && !r.IsDelete))
            {
                return ApiResult.Fail(ErrorCodes.DuplicateDisabled, "角色名称或代码已存在");
            }

            role.UpdateRole(dto.Name, dto.Code, dto.Role, dto.Description);
            await _roleRepository.UpdateAsync(role);

            return ApiResult.Success(new {
                Id = role.Id,
                Name = role.Name,
                Code = role.Code,
                RoleType = role.RoleType,
                Description = role.Description
            }, "角色更新成功");
        }
        catch (Exception ex)
        {
            return ApiResult.Fail(500, $"更新角色失败: {ex.Message}");
        }
    }
}