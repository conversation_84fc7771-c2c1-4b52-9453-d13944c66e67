using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Common;
using Application.Dtos;
using Application.Services;
using Domain.Entities.App;
using Domain.Repositories;
using Domain.Enums;

namespace Application.Services
{
    /// <summary>
    /// 用户服务实现类
    /// </summary>
    public class AppUserService : IAppUserService
    {
        private readonly IRepository<AppUser> _userRepository;
        private readonly IRepository<AppRole> _roleRepository;

        public AppUserService(IRepository<AppUser> userRepository, IRepository<AppRole> roleRepository)
        {
            _userRepository = userRepository;
            _roleRepository = roleRepository;
        }

        public async Task<dynamic> CreateAsync(CreateUserDto dto)
        {
            try
            {
                // 验证用户名是否已存在
                var existingUsers = await _userRepository.GetAllAsync();
                if (existingUsers.Any(u => u.Username == dto.Username && !u.IsDelete))
                {
                    return ApiResult.Fail(ErrorCodes.DuplicateDisabled, "用户名已存在");
                }

                // 创建新用户
                var newUser = new AppUser(dto.Username, dto.AvatarUrl, dto.Nickname)
                {
                    Id = Guid.NewGuid(),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    IsActive = true,
                    Role = Role.User, // 默认角色
                    Permission = Permission.Self // 默认权限
                };

                // 如果提供了密码，则设置密码
                if (!string.IsNullOrEmpty(dto.Password))
                {
                    var salt = Guid.NewGuid().ToString();
                    var hashedPassword = BCrypt.Net.BCrypt.HashPassword(dto.Password + salt);
                    newUser.Password = hashedPassword;
                    newUser.Salt = salt;
                }

                var result = await _userRepository.CreateAsync(newUser);
                
                return ApiResult.Success(new { 
                    Id = result.Id, 
                    Username = result.Username,
                    Nickname = result.Nickname,
                    AvatarUrl = result.AvatarUrl
                }, "用户创建成功");
            }
            catch (Exception ex)
            {
                return ApiResult.Fail(500, $"创建用户失败: {ex.Message}");
            }
        }

        public async Task<dynamic> GetAllAsync()
        {
            try
            {
                var users = await _userRepository.GetAllAsync();
                var userDtos = users.Where(u => !u.IsDelete).Select(u => new UserDto
                {
                    Id = u.Id,
                    Username = u.Username,
                    Nickname = u.Nickname,
                    AvatarUrl = u.AvatarUrl,
                    IsEnabled = u.IsActive,
                    Role = u.Role,
                    Permission = u.Permission,
                    CreatedAt = u.CreatedAt,
                    UpdatedAt = u.UpdatedAt
                }).ToList();

                return ApiResult.Success(userDtos, "获取用户列表成功");
            }
            catch (Exception ex)
            {
                return ApiResult.Fail(500, $"获取用户列表失败: {ex.Message}");
            }
        }

        public async Task<UserDto> GetByIdAsync(Guid Id)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(Id);
                if (user == null || user.IsDelete)
                {
                    return null;
                }

                return new UserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Nickname = user.Nickname,
                    AvatarUrl = user.AvatarUrl,
                    IsEnabled = user.IsActive,
                    Role = user.Role,
                    Permission = user.Permission,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt
                };
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task UpdateAsync(Guid id, UpdateUserDto dto)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(id);
                if (user == null || user.IsDelete)
                {
                    throw new InvalidOperationException("用户不存在");
                }

                // 更新用户信息
                if (!string.IsNullOrEmpty(dto.Nickname))
                {
                    user.Nickname = dto.Nickname;
                }

                if (!string.IsNullOrEmpty(dto.AvatarUrl))
                {
                    user.AvatarUrl = dto.AvatarUrl;
                }

                user.UpdatedAt = DateTime.UtcNow;
                await _userRepository.UpdateAsync(user);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"更新用户信息失败: {ex.Message}");
            }
        }

        public async Task DeleteAsync(Guid id)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(id);
                if (user == null || user.IsDelete)
                {
                    throw new InvalidOperationException("用户不存在");
                }

                // 软删除
                user.IsDelete = true;
                user.UpdatedAt = DateTime.UtcNow;
                await _userRepository.UpdateAsync(user);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"删除用户失败: {ex.Message}");
            }
        }

        public async Task<ApiResult> DisbleAsync(Guid id)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(id);
                if (user == null || user.IsDelete)
                {
                    return ApiResult.Fail(ErrorCodes.NotFound, "用户不存在");
                }

                user.IsActive = false;
                user.UpdatedAt = DateTime.UtcNow;
                await _userRepository.UpdateAsync(user);

                return ApiResult.Success(new { Id = user.Id, IsEnabled = user.IsActive }, "用户已禁用");
            }
            catch (Exception ex)
            {
                return ApiResult.Fail(500, $"禁用用户失败: {ex.Message}");
            }
        }

        public async Task AssignRoleAsync(Guid userId, Guid roleId)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null || user.IsDelete)
                {
                    throw new InvalidOperationException("用户不存在");
                }

                var role = await _roleRepository.GetByIdAsync(roleId);
                if (role == null || role.IsDelete)
                {
                    throw new InvalidOperationException("角色不存在");
                }

                user.Role = role.RoleType;
                user.UpdatedAt = DateTime.UtcNow;
                await _userRepository.UpdateAsync(user);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"分配角色失败: {ex.Message}");
            }
        }

        public async Task RemoveRoleAsync(Guid userId, Guid roleId)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null || user.IsDelete)
                {
                    throw new InvalidOperationException("用户不存在");
                }

                // 重置为默认角色
                user.Role = Role.User;
                user.UpdatedAt = DateTime.UtcNow;
                await _userRepository.UpdateAsync(user);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"移除角色失败: {ex.Message}");
            }
        }

        public async Task UpdateAvatarAsync(Guid userId, string avatarUrl)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null || user.IsDelete)
                {
                    throw new InvalidOperationException("用户不存在");
                }

                user.AvatarUrl = avatarUrl;
                user.UpdatedAt = DateTime.UtcNow;
                await _userRepository.UpdateAsync(user);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"更新头像失败: {ex.Message}");
            }
        }

        public async Task UpdateNickNameAsync(Guid userId, string nikename)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null || user.IsDelete)
                {
                    throw new InvalidOperationException("用户不存在");
                }

                user.Nickname = nikename;
                user.UpdatedAt = DateTime.UtcNow;
                await _userRepository.UpdateAsync(user);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"更新昵称失败: {ex.Message}");
            }
        }

        public async Task ChangPasswordAsync(Guid userId, string OldPwd, string newPwd)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null || user.IsDelete)
                {
                    throw new InvalidOperationException("用户不存在");
                }

                // 验证旧密码
                var oldPasswordWithSalt = OldPwd + user.Salt;
                if (!BCrypt.Net.BCrypt.Verify(oldPasswordWithSalt, user.Password))
                {
                    throw new InvalidOperationException("原密码错误");
                }

                // 设置新密码
                var newSalt = Guid.NewGuid().ToString();
                var newHashedPassword = BCrypt.Net.BCrypt.HashPassword(newPwd + newSalt);
                
                user.changePassWord(user.Username, newHashedPassword, newSalt);
                await _userRepository.UpdateAsync(user);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"修改密码失败: {ex.Message}");
            }
        }
    }
}
