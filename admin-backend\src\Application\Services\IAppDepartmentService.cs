namespace Application.Services;

/// <summary>
/// 部门服务接口
/// </summary>
public interface IAppDepartmentService
{
    /// <summary>
    /// 获取所有部门
    /// </summary>
    /// <returns>部门列表</returns>
    Task<dynamic> GetAllDepartmentsAsync();

    /// <summary>
    /// 根据ID获取部门
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <returns>部门信息</returns>
    Task<dynamic> GetDepartmentByIdAsync(Guid id);
}
