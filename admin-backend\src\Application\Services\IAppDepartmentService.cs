using Application.Dto;
using Application.Dtos;
using System;
namespace Application.Services;
public interface IAppDepartmentService
{
    Task<dynamic> CreateDepartmentAsync(CreateDepartmentDto dto);
    Task<dynamic> UpdateDepartmentAsync(Guid id, UpdateDepartmentDto dto);
    Task<dynamic> DeleteDepartmentAsync(Guid id);
    Task<dynamic> GetAllDepartmentAsync();
    Task<dynamic> GetByIdDepartmentAsync(Guid id);
    Task<dynamic> EnableDepartment(Guid DepartmentId);
