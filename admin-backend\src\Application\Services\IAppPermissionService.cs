using Application.Dtos;
using System;

namespace Application.Services;

public interface IAppPermissionService
{
    Task<dynamic> CreatePermissionAsync(CreatePermissionDto dto);
    Task<dynamic> DeletePermissionAsync(Guid permissionId);
    Task<dynamic> UpdatePermissionAsync(Guid permissionId, UpdatePermissionDto dto);
    Task<dynamic> EnablePermissionAsync(Guid permissionId);
    Task<dynamic> DisablePermissionAsync(Guid permissionId);
    Task<dynamic> GetAllPermissionsAsync();
    Task<dynamic> GetPermissionByIdAsync(Guid permissionId);
}