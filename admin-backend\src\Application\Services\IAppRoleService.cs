using Application.Dtos;

namespace Application.Services;

public interface IAppRoleService
{
    Task<dynamic> CreateRoleAsync(CreateRoleDto dto);
    Task<dynamic> UpdateRoleAsync(Guid id, UpdateRoleDto dto);
    Task<dynamic> DeleteRoleAsync(Guid id);
    Task<dynamic> GetAllRoleAsync();
    Task<dynamic> GetByIdRoleAsync(Guid id);
    Task<dynamic> EnableRole(Guid roleId);
    Task<dynamic> DisableRole(Guid roleId);
    Task<dynamic> AssignPermissionAsync(Guid roleId, string permissionCode);
    Task<dynamic> RemovePermissionAsync(Guid roleId, string permissionCode);
}