using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Application.Common;
using Application.Dtos;
using Domain.Enums;

namespace Application.Services
{
    public interface IAppUserService
    {
       Task<dynamic> CreateAsync(CreateUserDto dto);
       Task<dynamic> GetAllAsync();
       Task<UserDto> GetByIdAsync(Guid Id);
       Task UpdateAsync(Guid id, UpdateUserDto dto);
       Task DeleteAsync(Guid id);
       Task<ApiResult> DisbleAsync(Guid id);
       Task AssignRoleAsync(Guid userId,Guid roleId);
       Task RemoveRoleAsync(Guid userId,Guid roleId);
       Task UpdateAvatarAsync(Guid userId,string avatarUrl );
       Task UpdateNickNameAsync(Guid userId,string nikename );
       Task ChangPasswordAsync(Guid userId,string OldPwd,string newPwd );
       
    }
} 