namespace Domain.Common;

public abstract class EntityBase : IEntity
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public bool IsDelete { get; set; }
    public bool IsActive { get; set; }
    public Guid CreatedBy { get; set; }
    public Guid UpdatedBy { get; set; }
    public EntityBase()
    {
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        IsDelete = false;
        IsActive = true;
        UpdatedBy = Guid.Empty;
        CreatedBy = Guid.Empty;
    }
}