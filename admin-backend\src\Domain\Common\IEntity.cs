namespace Domain.Common;

/// <summary>
/// 实体接口
/// </summary>
public interface IEntity
{
    /// <summary>
    /// 实体ID
    /// </summary>
    Guid Id { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    bool IsActive { get; set; }

    /// <summary>
    /// 是否删除
    /// </summary>
    bool IsDelete { get; set; }
}
