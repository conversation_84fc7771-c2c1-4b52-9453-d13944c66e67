namespace Domain.Common;

/// <summary>
/// 分页扩展方法
/// </summary>
public static class PaginationExtensions
{
    /// <summary>
    /// 转换为分页结果
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="request">分页请求</param>
    /// <returns>分页结果</returns>
    public static PaginationResponse<T> ToPaginatedList<T>(this IQueryable<T> source, PaginationRequest request)
    {
        request.Validate();

        var totalCount = source.Count();
        var items = source.Skip((request.PageNumber - 1) * request.PageSize)
                          .Take(request.PageSize)
                          .ToList();

        return new PaginationResponse<T>(items, request.PageNumber, request.PageSize, totalCount);
    }

    /// <summary>
    /// 转换为分页结果
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="request">分页请求</param>
    /// <returns>分页结果</returns>
    public static PaginationResponse<T> ToPaginatedList<T>(this IEnumerable<T> source, PaginationRequest request)
    {
        request.Validate();

        var totalCount = source.Count();
        var items = source.Skip((request.PageNumber - 1) * request.PageSize)
                          .Take(request.PageSize)
                          .ToList();

        return new PaginationResponse<T>(items, request.PageNumber, request.PageSize, totalCount);
    }
}