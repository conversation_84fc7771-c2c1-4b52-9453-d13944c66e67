using Domain.Common;

namespace Domain.Entities.App; 
public class Department : EntityBase
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    
    /// <summary>
    /// 上级部门ID
    /// </summary>
    public Guid? ParentId { get; set; }
    
    /// <summary>
    /// 上级部门
    /// </summary>
    public Department? Parent { get; set; }
    
    /// <summary>
    /// 部门编码
    /// </summary>
    public string Code { get; set; } = null!;
    
    /// <summary>
    /// 排序号
    /// </summary>
    public int OrderNum { get; set; }

    /// <summary>
    /// 创建部门
    /// </summary>
    public Department(string name, string code, string? description = null, Guid? parentId = null)
    {
        Name = name;
        Code = code;
        Description = description;
        ParentId = parentId;
        OrderNum = 0;
    }

    /// <summary>
    /// 更新部门基本信息
    /// </summary>
    public void UpdateInfo(string name, string? description = null)
    {
        Name = name;
        if (description != null)
        {
            Description = description;
        }
        UpdatedAt = DateTime.Now;
    }

    /// <summary>
    /// 更新部门编码
    /// </summary>
    public void UpdateCode(string newCode)
    {
        Code = newCode;
        UpdatedAt = DateTime.Now;
    }

    /// <summary>
    /// 更改上级部门
    /// </summary>
    public void ChangeParent(Guid? newParentId)
    {
        ParentId = newParentId;
        UpdatedAt = DateTime.Now;
    }

    /// <summary>
    /// 设置排序号
    /// </summary>
    public void SetOrder(int orderNum)
    {
        OrderNum = orderNum;
        UpdatedAt = DateTime.Now;
    }

    /// <summary>
    /// 停用部门
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.Now;
    }

    /// <summary>
    /// 启用部门
    /// </summary>
    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.Now;
    }
}
