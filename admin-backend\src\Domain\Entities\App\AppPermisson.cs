using System;
using Domain.Enums;
using Domain.Common;

namespace Domain.Entities.App;

public class AppPermission : EntityBase
{
    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;
    public string? Description { get; set; }
    public Permission Permission { get; set; }
    public bool IsEnabled { get; set; }

    private AppPermission()
    {
        Name = string.Empty;
        Code = string.Empty;
    }

    public AppPermission(string name, string code, string? description, Permission permission)
    {
        Name = name;
        Code = code;
        Description = description;
        Permission = permission;
        IsEnabled = true;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }
}