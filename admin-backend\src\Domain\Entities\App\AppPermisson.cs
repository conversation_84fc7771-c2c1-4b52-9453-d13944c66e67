using System;
using Domain.Enums;
using Domain.Common;

namespace Domain.Entities.App;

public class AppPermission : EntityBase
{
    public string Name { get; set; }
    public string Code { get; set; }
    public string Description { get; set; }
    public Permission Permission { get; set; }
    public bool IsEnabled { get; set; }
    public new DateTime CreatedAt { get; set; }
    public new DateTime? UpdatedAt { get; set; }

    private AppPermission() { }

    public AppPermission(string name, string code, string description, Permission permission)
    {
        Name = name;
        Code = code;
        Description = description;
        Permission = permission;
        IsEnabled = true;
        CreatedAt = DateTime.UtcNow;
    }
}