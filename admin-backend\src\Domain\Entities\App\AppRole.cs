using System;
using Domain.Common;
using Domain.Enums;

namespace Domain.Entities.App;

public class AppRole : EntityBase
{
    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;
    public Role RoleType { get; set; }
    public string? Description { get; set; }

    /// <summary>
    /// 权限代码列表，用逗号分隔
    /// </summary>
    public string? PermissionCodes { get; set; }

    public AppRole(string name, string code, Role roleType, string? description = null)
    {
        Name = name;
        Code = code;
        RoleType = roleType;
        Description = description;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        IsActive = true;
    }

    private AppRole()
    {
        Name = string.Empty;
        Code = string.Empty;
    }
    public void UpdateRole(string newName, string newCode, Role newRoleType, string? newDescription = null)
    {
        if (!string.IsNullOrEmpty(newName) && !string.IsNullOrEmpty(newCode))
        {
            Name = newName;
            Code = newCode;
            RoleType = newRoleType;
            Description = newDescription;
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 分配权限
    /// </summary>
    /// <param name="permissionCode">权限代码</param>
    public void AssignPermission(string permissionCode)
    {
        if (string.IsNullOrEmpty(permissionCode)) return;

        var permissions = GetPermissionList();
        if (!permissions.Contains(permissionCode))
        {
            permissions.Add(permissionCode);
            PermissionCodes = string.Join(",", permissions);
            UpdatedAt = DateTime.Now;
        }
    }

    /// <summary>
    /// 移除权限
    /// </summary>
    /// <param name="permissionCode">权限代码</param>
    public void RemovePermission(string permissionCode)
    {
        if (string.IsNullOrEmpty(permissionCode)) return;

        var permissions = GetPermissionList();
        if (permissions.Remove(permissionCode))
        {
            PermissionCodes = permissions.Count > 0 ? string.Join(",", permissions) : null;
            UpdatedAt = DateTime.Now;
        }
    }

    /// <summary>
    /// 检查是否有指定权限
    /// </summary>
    /// <param name="permissionCode">权限代码</param>
    /// <returns></returns>
    public bool HasPermission(string permissionCode)
    {
        if (string.IsNullOrEmpty(permissionCode)) return false;
        return GetPermissionList().Contains(permissionCode);
    }

    /// <summary>
    /// 获取权限列表
    /// </summary>
    /// <returns></returns>
    public List<string> GetPermissionList()
    {
        if (string.IsNullOrEmpty(PermissionCodes))
            return [];

        return PermissionCodes.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
    }
}