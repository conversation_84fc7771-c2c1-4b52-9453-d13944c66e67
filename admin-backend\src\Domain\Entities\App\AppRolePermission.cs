using Domain.Common;

namespace Domain.Entities.App
{
    /// <summary>
    /// 角色权限关联实体
    /// </summary>
    public class AppRolePermission : EntityBase
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public Guid RoleId { get; set; }

        /// <summary>
        /// 权限ID
        /// </summary>
        public Guid PermissionId { get; set; }

        /// <summary>
        /// 角色导航属性
        /// </summary>
        public AppRole Role { get; set; } = null!;

        /// <summary>
        /// 权限导航属性
        /// </summary>
        public AppPermission Permission { get; set; } = null!;

        /// <summary>
        /// 无参构造函数
        /// </summary>
        private AppRolePermission() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="permissionId">权限ID</param>
        public AppRolePermission(Guid roleId, Guid permissionId)
        {
            RoleId = roleId;
            PermissionId = permissionId;
            CreatedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
            IsActive = true;
        }

        /// <summary>
        /// 激活关联
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// 停用关联
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
