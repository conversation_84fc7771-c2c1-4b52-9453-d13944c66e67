namespace Domain.Entities.App;

using Domain.Common;
using Domain.Enums;

public class AppUser : EntityBase
{
    public string Username { get; set; } = null!;
    public string Password { get; set; } = null!;
    public string Salt { get; set; } = null!;
    ///  <Summary>
    ///    头像
    /// </Summary>
    public string? AvatarUrl { get; set; }
    public Role Role { get; set; }
    public Permission Permission { get; set; }
    public string? Nickname { get; set; }
    public Department Department { get; set; }
   
    ///  <Summary>
    ///    分配角色
    /// </Summary>
    /// 
    public void AssignRole(string username, Role role)
    {
        Username = username;
        Role = role;
        UpdatedAt = DateTime.Now;

    }
    public void changePassWord(string username, string newPassword,string newSalt)
    {
        Password = newPassword;
        Salt = newSalt;
        UpdatedAt = DateTime.Now;
    }



    public AppUser(string username, string? AvatarUrl, string? nickname){
    if (username != null&& nickname != null)
    {     Username = username;
        AvatarUrl = username;
        Nickname = username;
        UpdatedAt = DateTime.Now;}
   
    }
public void ChangeRole( Role newRole,Permission newPermission){
    Role = newRole;
    Permission = newPermission;
}

}
