### BackStagePro API 测试文件
### 使用 VS Code REST Client 扩展或其他HTTP客户端工具测试

@baseUrl = https://localhost:7225
@contentType = application/json

### ===========================================
### 权限管理 API 测试
### ===========================================

### 1. 获取权限列表
GET {{baseUrl}}/api/permission
Content-Type: {{contentType}}

### 2. 创建权限
POST {{baseUrl}}/api/permission
Content-Type: {{contentType}}

{
  "name": "用户管理",
  "code": "USER_MANAGE",
  "description": "用户管理权限",
  "permission": 0
}

### 3. 获取权限详情 (需要替换实际的权限ID)
GET {{baseUrl}}/api/permission/{{permissionId}}
Content-Type: {{contentType}}

### 4. 更新权限 (需要替换实际的权限ID)
PUT {{baseUrl}}/api/permission/{{permissionId}}
Content-Type: {{contentType}}

{
  "name": "用户管理(更新)",
  "code": "USER_MANAGE_UPDATED",
  "description": "更新后的用户管理权限",
  "permission": 1
}

### 5. 启用权限 (需要替换实际的权限ID)
PATCH {{baseUrl}}/api/permission/{{permissionId}}/enable
Content-Type: {{contentType}}

### 6. 禁用权限 (需要替换实际的权限ID)
PATCH {{baseUrl}}/api/permission/{{permissionId}}/disable
Content-Type: {{contentType}}

### 7. 删除权限 (需要替换实际的权限ID)
DELETE {{baseUrl}}/api/permission/{{permissionId}}
Content-Type: {{contentType}}

### ===========================================
### 角色管理 API 测试
### ===========================================

### 1. 获取角色列表
GET {{baseUrl}}/api/role
Content-Type: {{contentType}}

### 2. 创建角色
POST {{baseUrl}}/api/role
Content-Type: {{contentType}}

{
  "name": "管理员",
  "code": "ADMIN",
  "description": "系统管理员角色",
  "role": 0
}

### 3. 获取角色详情 (需要替换实际的角色ID)
GET {{baseUrl}}/api/role/{{roleId}}
Content-Type: {{contentType}}

### 4. 更新角色 (需要替换实际的角色ID)
PUT {{baseUrl}}/api/role/{{roleId}}
Content-Type: {{contentType}}

{
  "name": "超级管理员",
  "code": "SUPER_ADMIN",
  "description": "超级管理员角色",
  "role": 0
}

### 5. 启用角色 (需要替换实际的角色ID)
PATCH {{baseUrl}}/api/role/{{roleId}}/enable
Content-Type: {{contentType}}

### 6. 禁用角色 (需要替换实际的角色ID)
PATCH {{baseUrl}}/api/role/{{roleId}}/disable
Content-Type: {{contentType}}

### 7. 为角色分配权限 (需要替换实际的角色ID和权限代码)
POST {{baseUrl}}/api/role/{{roleId}}/permissions/USER_MANAGE
Content-Type: {{contentType}}

### 8. 移除角色权限 (需要替换实际的角色ID和权限代码)
DELETE {{baseUrl}}/api/role/{{roleId}}/permissions/USER_MANAGE
Content-Type: {{contentType}}

### 9. 删除角色 (需要替换实际的角色ID)
DELETE {{baseUrl}}/api/role/{{roleId}}
Content-Type: {{contentType}}

### ===========================================
### 用户管理 API 测试
### ===========================================

### 1. 获取用户列表
GET {{baseUrl}}/api/user
Content-Type: {{contentType}}

### 2. 创建用户
POST {{baseUrl}}/api/user
Content-Type: {{contentType}}

{
  "username": "testuser",
  "password": "123456",
  "nickname": "测试用户",
  "avatarUrl": "https://example.com/avatar.jpg"
}

### 3. 获取用户详情 (需要替换实际的用户ID)
GET {{baseUrl}}/api/user/{{userId}}
Content-Type: {{contentType}}

### 4. 更新用户 (需要替换实际的用户ID)
PUT {{baseUrl}}/api/user/{{userId}}
Content-Type: {{contentType}}

{
  "nickname": "更新后的昵称",
  "avatarUrl": "https://example.com/new-avatar.jpg"
}

### 5. 禁用用户 (需要替换实际的用户ID)
PATCH {{baseUrl}}/api/user/{{userId}}/disable
Content-Type: {{contentType}}

### 6. 为用户分配角色 (需要替换实际的用户ID和角色ID)
POST {{baseUrl}}/api/user/{{userId}}/roles/{{roleId}}
Content-Type: {{contentType}}

### 7. 移除用户角色 (需要替换实际的用户ID和角色ID)
DELETE {{baseUrl}}/api/user/{{userId}}/roles/{{roleId}}
Content-Type: {{contentType}}

### 8. 更新用户头像 (需要替换实际的用户ID)
PATCH {{baseUrl}}/api/user/{{userId}}/avatar
Content-Type: {{contentType}}

"https://example.com/new-avatar.jpg"

### 9. 更新用户昵称 (需要替换实际的用户ID)
PATCH {{baseUrl}}/api/user/{{userId}}/nickname
Content-Type: {{contentType}}

"新昵称"

### 10. 修改用户密码 (需要替换实际的用户ID)
PATCH {{baseUrl}}/api/user/{{userId}}/password
Content-Type: {{contentType}}

{
  "oldPassword": "123456",
  "newPassword": "newpassword123"
}

### 11. 删除用户 (需要替换实际的用户ID)
DELETE {{baseUrl}}/api/user/{{userId}}
Content-Type: {{contentType}}

### ===========================================
### 参数验证测试
### ===========================================

### 测试权限创建参数验证
POST {{baseUrl}}/api/permission
Content-Type: {{contentType}}

{
  "name": "",
  "code": "invalid-code",
  "description": "这是一个超过200个字符的描述测试这是一个超过200个字符的描述测试这是一个超过200个字符的描述测试这是一个超过200个字符的描述测试这是一个超过200个字符的描述测试这是一个超过200个字符的描述测试这是一个超过200个字符的描述测试",
  "permission": 999
}

### 测试用户创建参数验证
POST {{baseUrl}}/api/user
Content-Type: {{contentType}}

{
  "username": "",
  "password": "123",
  "nickname": "这是一个超过50个字符的昵称测试这是一个超过50个字符的昵称测试",
  "avatarUrl": "invalid-url"
}

### ===========================================
### 错误处理测试
### ===========================================

### 测试不存在的资源
GET {{baseUrl}}/api/permission/00000000-0000-0000-0000-000000000000
Content-Type: {{contentType}}

### 测试无效的ID格式
GET {{baseUrl}}/api/permission/invalid-id
Content-Type: {{contentType}}

### 测试不存在的端点
GET {{baseUrl}}/api/nonexistent
Content-Type: {{contentType}}
